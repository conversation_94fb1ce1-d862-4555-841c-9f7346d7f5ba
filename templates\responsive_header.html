<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Header Design</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 4px 30px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header Styles */
        .header {
            background: var(--white);
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
        }

        /* Logo Section */
        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 700;
            font-size: 1.5rem;
            transition: var(--transition);
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.8rem;
            color: var(--white);
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);
        }

        .logo-text {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Navigation Links */
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-links li {
            position: relative;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            transition: var(--transition);
            z-index: -1;
        }

        .nav-links a:hover::before {
            left: 0;
        }

        .nav-links a:hover {
            color: var(--white);
            transform: translateY(-2px);
        }

        /* Dropdown Menu */
        .dropdown {
            position: relative;
        }

        .dropdown-content {
            position: absolute;
            top: 100%;
            left: 0;
            background: var(--white);
            min-width: 200px;
            box-shadow: var(--shadow-hover);
            border-radius: var(--border-radius);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition);
            z-index: 1000;
        }

        .dropdown:hover .dropdown-content {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-content a {
            display: block;
            padding: 0.8rem 1.2rem;
            color: var(--text-dark);
            text-decoration: none;
            transition: var(--transition);
            border-radius: 0;
        }

        .dropdown-content a:hover {
            background: var(--bg-light);
            color: var(--primary-blue);
            transform: translateX(5px);
        }

        /* Right Section */
        .nav-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Search Bar */
        .search-container {
            position: relative;
        }

        .search-bar {
            background: var(--bg-light);
            border: 2px solid transparent;
            border-radius: 25px;
            padding: 0.6rem 1.2rem 0.6rem 2.8rem;
            width: 300px;
            font-size: 0.9rem;
            transition: var(--transition);
            outline: none;
        }

        .search-bar:focus {
            border-color: var(--primary-blue);
            background: var(--white);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            transition: var(--transition);
        }

        .search-bar:focus + .search-icon {
            color: var(--primary-blue);
        }

        /* User Section */
        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            border: none;
            border-radius: 25px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 74, 173, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            color: var(--white);
            border: 2px solid transparent;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(205, 32, 139, 0.4);
        }

        /* Notification & Profile */
        .notification-btn {
            position: relative;
            background: var(--bg-light);
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            color: var(--text-dark);
        }

        .notification-btn:hover {
            background: var(--primary-blue);
            color: var(--white);
            transform: scale(1.1);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--primary-pink);
            color: var(--white);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .profile-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: 3px solid var(--primary-blue);
            overflow: hidden;
            cursor: pointer;
            transition: var(--transition);
        }

        .profile-btn:hover {
            transform: scale(1.1);
            border-color: var(--primary-pink);
        }

        .profile-btn img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Mobile Menu Section */
        .mobile-menu-section {
            display: none; /* Hidden by default (desktop view) */
            align-items: center;
            gap: 0.8rem;
            position: absolute;
            left: 0.5rem;
            z-index: 1001;
        }

        /* Mobile Profile Trigger */
        .mobile-profile-trigger {
            position: relative;
            cursor: pointer;
            transition: var(--transition);
        }

        .mobile-profile-trigger img {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: 2px solid rgba(0, 74, 173, 0.2);
            transition: var(--transition);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .mobile-profile-trigger:hover img {
            border-color: var(--primary-blue);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.2);
        }

        .mobile-profile-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 12px;
            height: 12px;
            background: #10b981;
            border: 2px solid var(--white);
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* Mobile Menu Button */
        .mobile-menu-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 74, 173, 0.1);
            color: var(--primary-blue);
            font-size: 1.1rem;
            cursor: pointer;
            padding: 0.4rem;
            border-radius: 8px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .mobile-menu-btn:hover {
            background: var(--primary-blue);
            color: var(--white);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.2);
        }

        /* Mobile Navigation */
        .mobile-nav {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--white);
            box-shadow: var(--shadow-hover);
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            padding: 1.5rem;
            transform: translateY(-20px);
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .mobile-nav.active {
            display: block;
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .mobile-nav-links {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .mobile-nav-links li {
            margin-bottom: 0.5rem;
        }

        .mobile-nav-links a {
            display: block;
            padding: 0.8rem 1rem;
            color: var(--text-dark);
            text-decoration: none;
            border-radius: 8px;
            transition: var(--transition);
        }

        .mobile-nav-links a:hover {
            background: var(--bg-light);
            color: var(--primary-blue);
        }

        /* Mobile Profile Header */
        .mobile-profile-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            margin: -1.5rem -1.5rem 1.5rem -1.5rem;
        }

        .mobile-profile-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            color: var(--white);
        }

        .mobile-profile-avatar {
            position: relative;
            margin-bottom: 1rem;
        }

        .mobile-profile-avatar img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .online-indicator {
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 20px;
            height: 20px;
            background: #10b981;
            border: 3px solid var(--white);
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .mobile-profile-info h3 {
            margin: 0 0 0.3rem 0;
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--white);
        }

        .mobile-profile-info p {
            margin: 0 0 0.5rem 0;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .user-role {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .mobile-profile-stats {
            display: flex;
            gap: 2rem;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--white);
            line-height: 1;
        }

        .stat-label {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 0.2rem;
        }

        /* Enhanced Mobile Navigation Links */
        .mobile-nav-links {
            list-style: none;
            margin-bottom: 1.5rem;
            padding: 0;
        }

        .mobile-nav-links li {
            margin-bottom: 0.3rem;
        }

        .mobile-nav-links a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.2rem;
            color: var(--text-dark);
            text-decoration: none;
            border-radius: 12px;
            transition: var(--transition);
            font-weight: 500;
            background: var(--white);
            border: 1px solid rgba(0, 74, 173, 0.1);
        }

        .mobile-nav-links a:hover {
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.05), rgba(205, 32, 139, 0.05));
            color: var(--primary-blue);
            transform: translateX(5px);
            border-color: rgba(0, 74, 173, 0.2);
        }

        .mobile-nav-links a i {
            width: 20px;
            text-align: center;
            color: var(--primary-blue);
            font-size: 1.1rem;
        }

        /* Enhanced Mobile User Actions */
        .mobile-user-actions {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(0, 74, 173, 0.1);
        }

        .mobile-action-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.2rem;
            color: var(--text-dark);
            text-decoration: none;
            border-radius: 12px;
            transition: var(--transition);
            background: var(--white);
            border: 1px solid rgba(0, 74, 173, 0.1);
            position: relative;
        }

        .mobile-action-link:hover {
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.05), rgba(205, 32, 139, 0.05));
            transform: translateX(5px);
            border-color: rgba(0, 74, 173, 0.2);
        }

        .mobile-action-link i:first-child {
            width: 20px;
            text-align: center;
            color: var(--primary-blue);
            font-size: 1.1rem;
            flex-shrink: 0;
        }

        .action-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.2rem;
        }

        .action-title {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        .action-subtitle {
            font-size: 0.8rem;
            color: var(--text-light);
        }

        .mobile-action-link i:last-child {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .notification-count {
            background: var(--primary-pink);
            color: var(--white);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .mobile-action-link.logout {
            border-color: rgba(220, 53, 69, 0.2);
        }

        .mobile-action-link.logout:hover {
            background: rgba(220, 53, 69, 0.05);
            border-color: rgba(220, 53, 69, 0.3);
        }

        .mobile-action-link.logout i:first-child {
            color: #dc3545;
        }

        .mobile-action-link.logout .action-title {
            color: #dc3545;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .navbar {
                padding: 1rem 1.5rem;
            }

            .search-bar {
                width: 250px;
            }
        }

        /* Tablet and Mobile Transition */
        @media (max-width: 992px) {
            .search-bar {
                width: 200px;
            }

            .action-buttons {
                gap: 0.8rem;
            }

            .btn {
                padding: 0.5rem 1.2rem;
                font-size: 0.85rem;
            }
        }

        /* Mobile Layout */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
                flex-wrap: nowrap;
            }

            /* Hide desktop navigation */
            .nav-links {
                display: none !important;
            }

            .search-container {
                display: none !important;
            }

            .action-buttons {
                display: none !important;
            }

            /* Show mobile menu section */
            .mobile-menu-section {
                display: flex !important;
                order: 3;
            }

            .nav-right {
                gap: 1rem;
                flex-shrink: 0;
            }

            .logo {
                flex: 1;
                min-width: 0;
            }

            .logo-text {
                font-size: 1.2rem;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
                margin-right: 0.6rem;
            }

            /* Mobile navigation styling */
            .mobile-nav {
                display: block;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--white);
                box-shadow: var(--shadow-hover);
                border-radius: 0 0 var(--border-radius) var(--border-radius);
                padding: 1.5rem;
                transform: translateY(-20px);
                opacity: 0;
                visibility: hidden;
                transition: var(--transition);
                z-index: 999;
            }

            .mobile-nav.active {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }
        }

        /* Small Mobile Devices */
        @media (max-width: 480px) {
            .navbar {
                padding: 0.8rem;
            }

            .logo-text {
                font-size: 1.1rem;
            }

            .logo-icon {
                width: 35px;
                height: 35px;
                margin-right: 0.5rem;
                font-size: 1rem;
            }

            .notification-btn,
            .profile-btn {
                width: 38px;
                height: 38px;
            }

            .mobile-menu-section {
                gap: 0.6rem;
            }

            .mobile-profile-trigger img {
                width: 32px;
                height: 32px;
            }

            .mobile-menu-btn {
                padding: 0.3rem;
                font-size: 1.1rem;
                width: 32px;
                height: 32px;
            }

            .mobile-nav {
                padding: 1.2rem;
            }

            .mobile-nav-links a {
                padding: 0.7rem 0.8rem;
                font-size: 0.9rem;
            }

            .mobile-actions .btn {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }
        }

        /* Extra Small Devices */
        @media (max-width: 360px) {
            .navbar {
                padding: 0.6rem;
            }

            .logo-text {
                font-size: 1rem;
            }

            .logo-icon {
                width: 32px;
                height: 32px;
                margin-right: 0.4rem;
                font-size: 0.9rem;
            }

            .notification-btn,
            .profile-btn {
                width: 35px;
                height: 35px;
            }

            .mobile-menu-section {
                gap: 0.5rem;
            }

            .mobile-profile-trigger img {
                width: 30px;
                height: 30px;
            }

            .mobile-menu-btn {
                padding: 0.25rem;
                font-size: 1rem;
                width: 30px;
                height: 30px;
            }
        }

        /* Force mobile layout on touch devices */
        @media (hover: none) and (pointer: coarse) {
            .nav-links,
            .search-container,
            .action-buttons {
                display: none !important;
            }

            .mobile-menu-section {
                display: flex !important;
            }
        }

        /* Desktop layout enforcement */
        @media (min-width: 769px) {
            .navbar.desktop-layout .nav-links {
                display: flex !important;
            }

            .navbar.desktop-layout .search-container {
                display: block !important;
            }

            .navbar.desktop-layout .action-buttons {
                display: flex !important;
            }

            .navbar.desktop-layout .mobile-menu-section {
                display: none !important;
            }

            .navbar.desktop-layout .mobile-nav {
                display: none !important;
            }
        }

        /* Mobile layout enforcement */
        @media (max-width: 768px) {
            .navbar.mobile-layout .nav-links {
                display: none !important;
            }

            .navbar.mobile-layout .search-container {
                display: none !important;
            }

            .navbar.mobile-layout .action-buttons {
                display: none !important;
            }

            .navbar.mobile-layout .mobile-menu-section {
                display: flex !important;
            }
        }

        /* Transition effects for layout switching */
        .navbar {
            transition: all 0.3s ease;
        }

        .nav-links,
        .search-container,
        .action-buttons,
        .mobile-menu-section {
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        /* Demo Content */
        .demo-content {
            padding: 4rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .demo-content h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-content p {
            font-size: 1.2rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <!-- Logo -->
            <a href="#" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <span class="logo-text">GigGenius</span>
            </a>

            <!-- Navigation Links -->
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li class="dropdown">
                    <a href="#services">Services <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-content">
                        <a href="#web-design">Web Design</a>
                        <a href="#development">Development</a>
                        <a href="#marketing">Marketing</a>
                        <a href="#consulting">Consulting</a>
                    </div>
                </li>
                <li><a href="#portfolio">Portfolio</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>

            <!-- Right Section -->
            <div class="nav-right">
                <!-- Search Bar -->
                <div class="search-container">
                    <input type="text" class="search-bar" placeholder="Search...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <!-- User Section -->
                <div class="user-section">
                    <!-- Notification -->
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>

                    <!-- Profile -->
                    <div class="profile-btn">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face" alt="Profile">
                    </div>
                </div>

                <!-- Mobile Menu Section -->
                <div class="mobile-menu-section">
                    <!-- Mobile Profile Picture -->
                    <div class="mobile-profile-trigger" onclick="toggleMobileMenu()">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face" alt="Profile">
                        <div class="mobile-profile-indicator"></div>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <div class="mobile-nav" id="mobileNav">
                <!-- Mobile Profile Header -->
                <div class="mobile-profile-header">
                    <div class="mobile-profile-card">
                        <div class="mobile-profile-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face" alt="Profile">
                            <div class="online-indicator"></div>
                        </div>
                        <div class="mobile-profile-info">
                            <h3>John Doe</h3>
                            <p><EMAIL></p>
                            <span class="user-role">Premium Member</span>
                        </div>
                        <div class="mobile-profile-stats">
                            <div class="stat-item">
                                <span class="stat-number">12</span>
                                <span class="stat-label">Projects</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">4.9</span>
                                <span class="stat-label">Rating</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Links -->
                <ul class="mobile-nav-links">
                    <li><a href="#home"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="#about"><i class="fas fa-info-circle"></i> About</a></li>
                    <li><a href="#services"><i class="fas fa-briefcase"></i> Services</a></li>
                    <li><a href="#portfolio"><i class="fas fa-folder"></i> Portfolio</a></li>
                    <li><a href="#contact"><i class="fas fa-envelope"></i> Contact</a></li>
                </ul>

                <!-- Mobile User Actions -->
                <div class="mobile-user-actions">
                    <a href="#profile" class="mobile-action-link">
                        <i class="fas fa-user"></i>
                        <div class="action-content">
                            <span class="action-title">My Profile</span>
                            <span class="action-subtitle">View and edit profile</span>
                        </div>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    <a href="#settings" class="mobile-action-link">
                        <i class="fas fa-cog"></i>
                        <div class="action-content">
                            <span class="action-title">Settings</span>
                            <span class="action-subtitle">Account preferences</span>
                        </div>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    <a href="#notifications" class="mobile-action-link">
                        <i class="fas fa-bell"></i>
                        <div class="action-content">
                            <span class="action-title">Notifications</span>
                            <span class="action-subtitle">3 new messages</span>
                        </div>
                        <span class="notification-count">3</span>
                    </a>
                    <a href="#logout" class="mobile-action-link logout">
                        <i class="fas fa-sign-out-alt"></i>
                        <div class="action-content">
                            <span class="action-title">Logout</span>
                            <span class="action-subtitle">Sign out of account</span>
                        </div>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Demo Content -->
    <main class="demo-content">
        <h1>Responsive Header Design</h1>
        <p>This is a modern, fully responsive header with smooth animations, dropdown menus, search functionality, and mobile-first design. Perfect for any web application or website.</p>
    </main>

    <script>
        // Device detection functions
        function isMobileDevice() {
            return window.innerWidth <= 768 ||
                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
        }

        function isDesktopDevice() {
            return window.innerWidth > 768 &&
                   !(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
        }

        // Initialize layout based on device type
        function initializeLayout() {
            const navbar = document.querySelector('.navbar');
            const mobileNav = document.getElementById('mobileNav');
            const navLinks = document.querySelector('.nav-links');
            const searchContainer = document.querySelector('.search-container');
            const userSection = document.querySelector('.user-section');
            const mobileMenuSection = document.querySelector('.mobile-menu-section');

            if (isMobileDevice()) {
                // Mobile Layout
                navbar.classList.add('mobile-layout');
                navbar.classList.remove('desktop-layout');

                // Show mobile elements
                if (mobileMenuSection) mobileMenuSection.style.display = 'flex';

                // Hide desktop elements
                if (navLinks) navLinks.style.display = 'none';
                if (searchContainer) searchContainer.style.display = 'none';
                if (userSection) userSection.style.display = 'none'; // Hide desktop user section on mobile

                // Close mobile menu if it's open
                if (mobileNav && mobileNav.classList.contains('active')) {
                    closeMobileMenu();
                }
            } else {
                // Desktop Layout
                navbar.classList.add('desktop-layout');
                navbar.classList.remove('mobile-layout');

                // Show desktop elements
                if (navLinks) navLinks.style.display = 'flex';
                if (searchContainer) searchContainer.style.display = 'block';
                if (userSection) userSection.style.display = 'flex';

                // Hide mobile elements
                if (mobileMenuSection) mobileMenuSection.style.display = 'none';
                if (mobileNav) {
                    mobileNav.classList.remove('active');
                    mobileNav.style.display = 'none';
                }

                // Reset body scroll
                document.body.style.overflow = '';

                // Reset mobile menu icon
                const menuIcon = document.querySelector('.mobile-menu-btn i');
                if (menuIcon) {
                    menuIcon.classList.remove('fa-times');
                    menuIcon.classList.add('fa-bars');
                }
            }
        }

        // Mobile Menu Toggle
        function toggleMobileMenu() {
            const mobileNav = document.getElementById('mobileNav');
            const menuBtn = document.querySelector('.mobile-menu-btn i');

            mobileNav.classList.toggle('active');

            if (mobileNav.classList.contains('active')) {
                menuBtn.classList.remove('fa-bars');
                menuBtn.classList.add('fa-times');
                // Prevent body scroll when menu is open
                document.body.style.overflow = 'hidden';
            } else {
                menuBtn.classList.remove('fa-times');
                menuBtn.classList.add('fa-bars');
                // Restore body scroll
                document.body.style.overflow = '';
            }
        }

        // Close mobile menu
        function closeMobileMenu() {
            const mobileNav = document.getElementById('mobileNav');
            const menuIcon = document.querySelector('.mobile-menu-btn i');

            mobileNav.classList.remove('active');
            menuIcon.classList.remove('fa-times');
            menuIcon.classList.add('fa-bars');
            document.body.style.overflow = '';
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const mobileNav = document.getElementById('mobileNav');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileNav && menuBtn &&
                !menuBtn.contains(event.target) &&
                !mobileNav.contains(event.target) &&
                mobileNav.classList.contains('active')) {
                closeMobileMenu();
            }
        });

        // Close mobile menu when clicking on mobile nav links
        document.querySelectorAll('.mobile-nav-links a').forEach(link => {
            link.addEventListener('click', function() {
                closeMobileMenu();
            });
        });

        // Handle window resize with debouncing
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                const mobileNav = document.getElementById('mobileNav');

                // Close mobile menu if window is resized to desktop
                if (window.innerWidth > 768 && mobileNav && mobileNav.classList.contains('active')) {
                    closeMobileMenu();
                }

                // Re-initialize layout based on new screen size
                initializeLayout();
            }, 100); // Debounce resize events
        });

        // Search functionality (only if search bar exists)
        document.addEventListener('DOMContentLoaded', function() {
            const searchBar = document.querySelector('.search-bar');
            if (searchBar) {
                searchBar.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                searchBar.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Touch device detection and handling
        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
            document.body.classList.add('touch-device');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeLayout();
        });

        // Handle orientation change on mobile devices
        window.addEventListener('orientationchange', function() {
            setTimeout(function() {
                initializeLayout();
                if (document.getElementById('mobileNav').classList.contains('active')) {
                    closeMobileMenu();
                }
            }, 200); // Increased timeout for orientation change
        });

        // Handle visibility change (when switching between tabs/apps)
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // Re-initialize layout when page becomes visible
                setTimeout(function() {
                    initializeLayout();
                }, 100);
            }
        });

        // Additional check for browser back/forward navigation
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                // Page was loaded from cache, re-initialize
                initializeLayout();
            }
        });

        // Handle logout functionality
        function handleLogout() {
            // Add your logout logic here
            // Clear authentication data
            // localStorage.removeItem('userToken');
            // sessionStorage.removeItem('userSession');
            // document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

            // Close mobile menu if open
            const mobileNav = document.getElementById('mobileNav');
            if (mobileNav && mobileNav.classList.contains('active')) {
                closeMobileMenu();
            }

            // Redirect to login page or home page
            // window.location.href = '/login';
            console.log('User logged out');
        }

        // Add event listeners for logout links
        document.addEventListener('DOMContentLoaded', function() {
            // Desktop and mobile logout links
            document.querySelectorAll('a[href="#logout"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleLogout();
                });
            });
        });
    </script>
</body>
</html>