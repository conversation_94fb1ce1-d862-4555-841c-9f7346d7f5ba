<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Header Design</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 4px 30px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header Styles */
        .header {
            background: var(--white);
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
        }

        /* Logo Section */
        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 700;
            font-size: 1.5rem;
            transition: var(--transition);
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.8rem;
            color: var(--white);
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.3);
        }

        .logo-text {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Navigation Links */
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-links li {
            position: relative;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            transition: var(--transition);
            z-index: -1;
        }

        .nav-links a:hover::before {
            left: 0;
        }

        .nav-links a:hover {
            color: var(--white);
            transform: translateY(-2px);
        }

        /* Dropdown Menu */
        .dropdown {
            position: relative;
        }

        .dropdown-content {
            position: absolute;
            top: 100%;
            left: 0;
            background: var(--white);
            min-width: 200px;
            box-shadow: var(--shadow-hover);
            border-radius: var(--border-radius);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition);
            z-index: 1000;
        }

        .dropdown:hover .dropdown-content {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-content a {
            display: block;
            padding: 0.8rem 1.2rem;
            color: var(--text-dark);
            text-decoration: none;
            transition: var(--transition);
            border-radius: 0;
        }

        .dropdown-content a:hover {
            background: var(--bg-light);
            color: var(--primary-blue);
            transform: translateX(5px);
        }

        /* Right Section */
        .nav-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Search Bar */
        .search-container {
            position: relative;
        }

        .search-bar {
            background: var(--bg-light);
            border: 2px solid transparent;
            border-radius: 25px;
            padding: 0.6rem 1.2rem 0.6rem 2.8rem;
            width: 300px;
            font-size: 0.9rem;
            transition: var(--transition);
            outline: none;
        }

        .search-bar:focus {
            border-color: var(--primary-blue);
            background: var(--white);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            transition: var(--transition);
        }

        .search-bar:focus + .search-icon {
            color: var(--primary-blue);
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            border: none;
            border-radius: 25px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 74, 173, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            color: var(--white);
            border: 2px solid transparent;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(205, 32, 139, 0.4);
        }

        /* Notification & Profile */
        .notification-btn {
            position: relative;
            background: var(--bg-light);
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            color: var(--text-dark);
        }

        .notification-btn:hover {
            background: var(--primary-blue);
            color: var(--white);
            transform: scale(1.1);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--primary-pink);
            color: var(--white);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .profile-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: 3px solid var(--primary-blue);
            overflow: hidden;
            cursor: pointer;
            transition: var(--transition);
        }

        .profile-btn:hover {
            transform: scale(1.1);
            border-color: var(--primary-pink);
        }

        .profile-btn img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Mobile Menu Button */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-dark);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: var(--transition);
        }

        .mobile-menu-btn:hover {
            background: var(--bg-light);
            color: var(--primary-blue);
        }

        /* Mobile Navigation */
        .mobile-nav {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--white);
            box-shadow: var(--shadow-hover);
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            padding: 1.5rem;
            transform: translateY(-20px);
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .mobile-nav.active {
            display: block;
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .mobile-nav-links {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .mobile-nav-links li {
            margin-bottom: 0.5rem;
        }

        .mobile-nav-links a {
            display: block;
            padding: 0.8rem 1rem;
            color: var(--text-dark);
            text-decoration: none;
            border-radius: 8px;
            transition: var(--transition);
        }

        .mobile-nav-links a:hover {
            background: var(--bg-light);
            color: var(--primary-blue);
        }

        .mobile-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .navbar {
                padding: 1rem 1.5rem;
            }
            
            .search-bar {
                width: 250px;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
            }

            .nav-links,
            .search-container,
            .action-buttons {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .nav-right {
                gap: 1rem;
            }

            .logo-text {
                font-size: 1.2rem;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
                margin-right: 0.6rem;
            }
        }

        @media (max-width: 480px) {
            .navbar {
                padding: 0.8rem;
            }

            .logo-text {
                font-size: 1.1rem;
            }

            .logo-icon {
                width: 35px;
                height: 35px;
                margin-right: 0.5rem;
            }

            .notification-btn,
            .profile-btn {
                width: 40px;
                height: 40px;
            }
        }

        /* Demo Content */
        .demo-content {
            padding: 4rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .demo-content h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-content p {
            font-size: 1.2rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <!-- Logo -->
            <a href="#" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <span class="logo-text">GigGenius</span>
            </a>

            <!-- Navigation Links -->
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li class="dropdown">
                    <a href="#services">Services <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-content">
                        <a href="#web-design">Web Design</a>
                        <a href="#development">Development</a>
                        <a href="#marketing">Marketing</a>
                        <a href="#consulting">Consulting</a>
                    </div>
                </li>
                <li><a href="#portfolio">Portfolio</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>

            <!-- Right Section -->
            <div class="nav-right">
                <!-- Search Bar -->
                <div class="search-container">
                    <input type="text" class="search-bar" placeholder="Search...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="#login" class="btn btn-outline">Login</a>
                    <a href="#signup" class="btn btn-primary">Sign Up</a>
                </div>

                <!-- Notification -->
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>

                <!-- Profile -->
                <div class="profile-btn">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face" alt="Profile">
                </div>

                <!-- Mobile Menu Button -->
                <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="mobile-nav" id="mobileNav">
                <ul class="mobile-nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#portfolio">Portfolio</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <div class="mobile-actions">
                    <a href="#login" class="btn btn-outline">Login</a>
                    <a href="#signup" class="btn btn-primary">Sign Up</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Demo Content -->
    <main class="demo-content">
        <h1>Responsive Header Design</h1>
        <p>This is a modern, fully responsive header with smooth animations, dropdown menus, search functionality, and mobile-first design. Perfect for any web application or website.</p>
    </main>

    <script>
        // Mobile Menu Toggle
        function toggleMobileMenu() {
            const mobileNav = document.getElementById('mobileNav');
            const menuBtn = document.querySelector('.mobile-menu-btn i');
            
            mobileNav.classList.toggle('active');
            
            if (mobileNav.classList.contains('active')) {
                menuBtn.classList.remove('fa-bars');
                menuBtn.classList.add('fa-times');
            } else {
                menuBtn.classList.remove('fa-times');
                menuBtn.classList.add('fa-bars');
            }
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const mobileNav = document.getElementById('mobileNav');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (!menuBtn.contains(event.target) && !mobileNav.contains(event.target)) {
                mobileNav.classList.remove('active');
                document.querySelector('.mobile-menu-btn i').classList.remove('fa-times');
                document.querySelector('.mobile-menu-btn i').classList.add('fa-bars');
            }
        });

        // Search functionality
        document.querySelector('.search-bar').addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        document.querySelector('.search-bar').addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>